# 内容创作时间戳和数据来源功能说明

## 🎯 功能需求

1. **时间戳字段**：在内容创作模式创建会话时添加 `timestamp` 字段（系统当前时间戳）
2. **数据来源获取**：回答完问题后调用 `baiduRollback` 接口获取数据来源并记录日志

## 🔧 技术实现

### 1. 时间戳字段添加

#### 状态管理
```javascript
// 内容创作模式的时间戳，用于 baiduRollback 接口
const contentCreationTimestamp = ref(null);
```

#### Sessions 接口调用修改
在所有内容创作模式的 sessions 接口调用中添加时间戳：

```javascript
if (selectedMode.value.value === 'content_creation') {
    requestData.type = isNetworkEnabled.value ? 'intel' : 'local';
    requestData.kbId = kbId ? String(kbId) : null;
    
    // 添加时间戳字段
    requestData.timestamp = Date.now();
    // 保存时间戳用于后续 baiduRollback 调用
    contentCreationTimestamp.value = requestData.timestamp;
    
    // 添加文件内容参数
    if (uploadedFile.value && uploadedFile.value.content) {
        requestData.file = uploadedFile.value.content;
    }
    
    console.log('内容创作模式，使用kbId:', requestData.kbId, 'type:', requestData.type, 'timestamp:', requestData.timestamp);
}
```

#### 修改位置
1. **createSession 函数**（第一次创建会话）
2. **createSession 函数**（重新创建会话）
3. **连续对话部分**（sendMessage 函数中）

### 2. 数据来源获取功能

#### baiduRollback 接口调用
```javascript
// 调用 baiduRollback 接口获取数据来源
const callBaiduRollback = async () => {
    try {
        // 检查是否有保存的时间戳
        if (!contentCreationTimestamp.value) {
            console.log('没有找到内容创作时间戳，跳过 baiduRollback 调用');
            return;
        }
        
        console.log('内容创作模式回答完成，调用 baiduRollback 接口获取数据来源，时间戳:', contentCreationTimestamp.value);
        
        // 调用 baiduRollback 接口，传递时间戳
        const response = await baiduRollback(contentCreationTimestamp.value);
        
        console.log('baiduRollback 接口调用成功，获取到的数据来源:', response);
        
        // 如果有数据，记录详细信息
        if (response && response.data) {
            console.log('数据来源详情:', JSON.stringify(response.data, null, 2));
        } else {
            console.log('baiduRollback 接口返回空数据');
        }
        
    } catch (error) {
        console.error('调用 baiduRollback 接口失败:', error);
    }
};
```

#### 调用时机
在流式输出完成后调用：

```javascript
// 在 setTimeout 回调函数中
setTimeout(() => {
    // 确保思考步骤始终可见
    ensureThinkingStepsVisible();
    
    // 如果是内容创作模式，调用 baiduRollback 接口获取数据来源
    if (selectedMode.value.value === 'content_creation') {
        callBaiduRollback();
    }
}, 1000);
```

### 3. 生命周期管理

#### 时间戳清理
在创建新会话时清空时间戳：

```javascript
const createNewSession = async (clearMessages = true) => {
    // ... 其他清理逻辑 ...
    
    // 清空已上传的文件
    uploadedFile.value = null;
    console.log('已清空上传的文件');

    // 清空内容创作时间戳
    contentCreationTimestamp.value = null;
    console.log('已清空内容创作时间戳');
    
    // ... 其他逻辑 ...
};
```

## 📊 数据流程

### 1. 会话创建流程
```
用户发起对话 
→ 检查模式是否为 content_creation
→ 生成时间戳 timestamp = Date.now()
→ 保存到 contentCreationTimestamp.value
→ 调用 sessions 接口（包含 timestamp 字段）
→ 获取 sessionId
```

### 2. 回答完成流程
```
AI 回答完成
→ 检查模式是否为 content_creation
→ 检查是否有保存的时间戳
→ 调用 baiduRollback(timestamp) 接口
→ 记录数据来源日志
```

### 3. 会话清理流程
```
创建新会话
→ 清空 contentCreationTimestamp.value = null
→ 清空其他相关状态
```

## 🔍 调试和验证

### 控制台日志示例

#### 创建会话时
```
内容创作模式，使用kbId: null, type: local, timestamp: 1703123456789
已保存内容创作时间戳: 1703123456789
```

#### 回答完成时
```
内容创作模式回答完成，调用 baiduRollback 接口获取数据来源，时间戳: 1703123456789
baiduRollback 接口调用成功，获取到的数据来源: {data: {...}}
数据来源详情: {
  "source": "baidu",
  "references": [...],
  "timestamp": 1703123456789
}
```

#### 创建新会话时
```
已清空内容创作时间戳
```

### 网络请求验证

#### Sessions 接口请求体
```json
{
  "type": "local",
  "kbId": null,
  "timestamp": 1703123456789,
  "file": "文件内容..."
}
```

#### baiduRollback 接口调用
```
GET /app/api/system/ragflowApi/baiduRollback/1703123456789
```

## ⚠️ 注意事项

### 时间戳管理
- 时间戳只在内容创作模式下生成和使用
- 每次创建会话都会生成新的时间戳
- 创建新会话时会清空之前的时间戳

### 接口调用
- baiduRollback 接口只在内容创作模式下调用
- 只有在有保存的时间戳时才会调用
- 接口调用失败不会影响正常功能

### 错误处理
- 时间戳为空时跳过 baiduRollback 调用
- 接口调用失败时记录错误日志
- 不影响用户正常使用

## 📋 测试用例

### 测试步骤
1. 切换到内容创作模式
2. 发起对话
3. 验证 sessions 接口包含 timestamp 字段
4. 等待 AI 回答完成
5. 验证控制台显示 baiduRollback 调用日志
6. 验证数据来源信息被正确记录

### 边界情况测试
1. 非内容创作模式下不应生成时间戳
2. 时间戳为空时不应调用 baiduRollback
3. baiduRollback 接口失败时的错误处理
4. 创建新会话时时间戳应被清空

## 🚀 功能总结

现在内容创作模式具有完整的数据追踪功能：
- **时间戳生成** = 每次会话创建时生成唯一标识
- **数据来源追踪** = 回答完成后自动获取数据来源
- **日志记录** = 详细记录所有相关信息
- **生命周期管理** = 自动清理和重置状态

这确保了内容创作模式下的数据可追溯性和透明度。
